<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/10">
    <div class="settings-modal rounded-3xl bg-white dark:bg-black-70 w-[800px] h-[671px] flex overflow-hidden relative">
      <!-- 侧边栏 -->
      <div class="w-1/4 bg-[#FCFAF9] dark:bg-black-70 h-full flex flex-col items-start pt-8 px-4">
        <div class="text-[20px] font-semibold mb-6 text-black dark:text-white" style="font-family: 'Poppins', sans-serif;">Settings</div>
        <button class="settings-tab-btn" :class="{active: localTab==='settings'}" @click="localTab='settings'">
          <img :src="isDark ? '/image/profile-dark.png' : '/image/profile.png'" class="w-5 h-5 mr-1 iconImg" />Profile
        </button>
        <button class="settings-tab-btn" :class="{active: localTab==='verification'}" @click="localTab='verification'">
          <img :src="isDark ? '/image/verified-dark.png' : '/image/verified.png'" class="w-5 h-5 mr-1 iconImg" />Verification
        </button>
        <!-- <button class="settings-tab-btn" :class="{active: localTab==='subscription'}" @click="localTab='subscription'">
          <img src="/image/subscription.png" class="w-5 h-5 mr-1 iconImg" />Subscription
        </button> -->
      </div>
      <!-- 主内容区 -->
      <div class="flex-1 p-8 pt-[85px] relative">
        <div class="absolute right-4 top-4 text-2xl cursor-pointer" @click="$emit('close')">×</div>
        <template v-if="localTab==='settings'">
          <!-- 个人信息内容区 -->
          <div class="flex flex-col h-full">
            <!-- Edit Profile 标题区域 -->
            <div class="mb-6">
                             <div class="text-[16px] font-semibold mb-4" style="font-family: 'Poppins', sans-serif;">Edit Profile</div>
              <!-- 头像和基本信息 -->
              <div class="flex flex-col items-center">
                <template v-if="firebaseUser && firebaseUser.display_name">
                                     <div class="relative w-[90px] h-[90px] mb-3 group cursor-pointer">
                     <img :src="user?.profile_picture || firebaseUser?.photo_url" class="w-[90px] h-[90px] rounded-full object-cover" />
                    <div class="absolute inset-0 pointer-events-none">
                      <div class="avatar-gradient-mask flex items-end justify-center w-full h-full">
                        <img src="/image/camera.png" class="w-4 h-4 mb-2 pointer-events-auto" alt="Upload avatar" />
                      </div>
                      <input type="file" accept="image/*" class="absolute inset-0 opacity-0 cursor-pointer pointer-events-auto" @change="onAvatarChange" />
                      <div v-if="avatarLoading" class="absolute inset-0 flex flex-col items-center justify-center bg-white/90 rounded-full z-10">
                        <svg class="animate-spin mb-2" width="40" height="40" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="32" cy="32" r="28" stroke="#F5E7E1" stroke-width="8" />
                          <path d="M60 32c0-15.464-12.536-28-28-28" stroke="#C47A5A" stroke-width="8" stroke-linecap="round" />
                        </svg>
                        <div class="text-xs font-bold text-black">Uploading...</div>
                      </div>
                    </div>
                  </div>
                  <div class="font-bold text-base mb-1">
                    {{ user?.display_name ||  firebaseUser?.display_name || '--' }} 
                    <img src="/image/Edit.png" class="w-4 h-4 inline-block ml-1 cursor-pointer" @click="showEditNameModal = true" />
                  </div>
                  <div class="text-gray-400 text-xs mb-1">ID:{{ user?.user_id || '--' }}</div>
                </template>
                <template v-else>
                                     <!-- 骨架屏 -->
                   <div class="skeleton-avatar mb-3" style="width: 90px; height: 90px;"></div>
                  <div class="skeleton-line mb-2" style="width: 120px;"></div>
                  <div class="skeleton-line mb-4" style="width: 80px;"></div>
                </template>
              </div>
            </div>
            
            <!-- 分割线 -->
            <div class="w-full border-t border-gray-200 dark:border-[#2A2A2A] mb-6"></div>
            
            <!-- Account 标题和社交账号绑定区 -->
            <div class="flex-1">
                             <div class="text-[16px] font-semibold mb-4" style="font-family: 'Poppins', sans-serif;">Account</div>
              <div class="space-y-4">
                <template v-if="firebaseUser && firebaseUser.display_name">
                  <div class="flex items-center">
                    <div class="w-10 h-10 flex items-center justify-center rounded-full mr-3 border border-gray-200 dark:border-[#E4E6E899]">
                      <img src="/image/gmail.png" class="w-10 h-10 dark:invert" />
                    </div>
                                         <div class="flex-1">
                       <div class="text-[12px] text-gray-400" style="font-family: 'Poppins', sans-serif; font-weight: 400;">Email</div>
                       <div class="text-base text-black dark:text-white">{{ firebaseUser?.email || '--' }}</div>
                     </div>
                  </div>
                  <!-- 分割线 -->
                  <div class="w-full border-t border-gray-200 dark:border-[#2A2A2A] my-4"></div>
                  <div class="flex items-center">
                    <div class="w-10 h-10 flex items-center justify-center rounded-full mr-3 border border-gray-200 dark:border-[#E4E6E899]">
                      <img src="/image/x-icon.png" class="w-10 h-10 dark:invert" alt="X" />
                    </div>
                                         <div class="flex-1">
                       <div class="text-[#8D8D8D] text-[12px] leading-tight" style="font-family: 'Poppins', sans-serif; font-weight: 400;">X(Twitter)</div>
                       <div class="text-black dark:text-[#FAF9F5] text-base font-semibold leading-tight">{{ user?.twitter_url || '--' }}</div>
                     </div>
                    <button 
                      v-if="!user?.twitter_url || user?.twitter_url === '--'"
                      class="connect-btn"
                      @click="onConnect('twitter')"
                    >
                      Connect
                    </button>
                                         <button 
                       v-else
                       class="connect-btn"
                       style="background: #f5f5f5; color: #666; border: 1px solid #ddd;"
                       @click="onConnect('twitter')"
                     >
                       Edit
                     </button>
                  </div>
                  <!-- 分割线 -->
                  <div class="w-full border-t border-gray-200 dark:border-[#2A2A2A] my-4"></div>
                  <div class="flex items-center">
                    <div class="w-10 h-10 flex items-center justify-center rounded-full mr-3 border border-gray-200 dark:border-[#E4E6E899]">
                      <img src="/image/github-icon.png" class="w-10 h-10 dark:invert" alt="Github" />
                    </div>
                                         <div class="flex-1">
                       <div class="text-[#8D8D8D] text-[12px] leading-tight" style="font-family: 'Poppins', sans-serif; font-weight: 400;">Github</div>
                       <div class="text-black dark:text-[#FAF9F5] text-base font-semibold leading-tight">{{ user?.github_url || '--' }}</div>
                     </div>
                    <button 
                      v-if="!user?.github_url || user?.github_url === '--'"
                      class="connect-btn"
                      @click="onConnect('github')"
                    >
                      Connect
                    </button>
                                         <button 
                       v-else
                       class="connect-btn"
                       style="background: #f5f5f5; color: #666; border: 1px solid #ddd;"
                       @click="onConnect('github')"
                     >
                       Edit
                     </button>
                  </div>
                  <!-- 分割线 -->
                  <div class="w-full border-t border-gray-200 dark:border-[#2A2A2A] mt-4"></div>
                </template>
                <template v-else>
                  <!-- 骨架屏 -->
                  <div class="flex items-center gap-3 mb-4 w-full">
                    <div class="skeleton-icon"></div>
                    <div class="flex-1">
                      <div class="skeleton-line mb-2" style="width: 60px;"></div>
                      <div class="skeleton-line" style="width: 120px;"></div>
                    </div>
                  </div>
                  <div class="flex items-center gap-3 mb-4 w-full">
                    <div class="skeleton-icon"></div>
                    <div class="flex-1">
                      <div class="skeleton-line mb-2" style="width: 60px;"></div>
                      <div class="skeleton-line" style="width: 120px;"></div>
                    </div>
                  </div>
                  <div class="flex items-center gap-3 mb-4 w-full">
                    <div class="skeleton-icon"></div>
                    <div class="flex-1">
                      <div class="skeleton-line mb-2" style="width: 60px;"></div>
                      <div class="skeleton-line" style="width: 120px;"></div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
            
            <!-- 底部按钮区 -->
            <div class="mt-auto">
              <template v-if="firebaseUser && firebaseUser.display_name">
                                 <button class="signout-btn w-full border border-black dark:border-white rounded-full py-2 flex items-center justify-center gap-2 text-base" @click="handleSignout">
                   <img src="/image/signout.png" class="w-5 h-5" />Sign out
                 </button>
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="localTab==='verification'">
          <div class="flex flex-col items-center justify-center w-full h-full px-2">
            <template v-if="verificationLoading">
              <div class="flex items-center justify-center h-full">
                <div class="text-lg text-gray-400">Loading verification status...</div>
              </div>
            </template>
            <!-- <template v-else-if="!verificationStatus || !verificationStatus.exists || verificationStatus.verification.current_step !== 'completed'"> -->
            <template v-else-if="!verificationStatus || !verificationStatus.exists || verificationStatus.verification.current_step !== 'completed'">
              <!-- 未认证，展示认证流程入口 -->
              <div class="w-full max-w-[480px] mx-auto">
                <div class="text-xl text-black dark:text-[#FAF9F5] mb-4 mt-4"><span class="font-bold">For Job Seekers</span><span class="font-normal">, get verified to:</span></div>
                <ul class="mb-12 ml-2">
                  <li class="verification-li">Stand out and get better job offers</li>
                  <li class="verification-li">Gain a verified badge to boost trust</li>
                  <li class="verification-li">Access exclusive career tools</li>
                </ul>
                <div class="text-xl text-black dark:text-[#FAF9F5] mb-4 mt-4"><span class="font-bold">For Recruiters</span><span class="font-normal">, get verified to:</span></div>
                <ul class="mb-12 ml-2">
                  <li class="verification-li">Find top-quality, verified candidates faster</li>
                  <li class="verification-li">Improve your match accuracy</li>
                  <li class="verification-li">Save time on screening</li>
                </ul>
              </div>
              <div class="verification-btn flex items-center justify-center gap-2 mt-auto cursor-not-allowed relative group" 
                style="background-color: black;">
                <img src="/image/user12.png" class="w-6 h-6 text-white" alt="verify"/>
                <span style="font-weight: 400;">Verification Now!</span>
                <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white px-3 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                  Coming Soon
                </div>
              </div>
            </template>
            <template v-else-if="verificationStatus && verificationStatus.exists">
              <!-- 认证结果卡片 100%还原视觉稿 -->
              <div class="w-full max-w-[540px] mx-auto mt-8">
                <div class="relative rounded-2xl border-2 border-dashed border-[#C7D6F3] bg-[#F6EEE9] p-6 flex items-center mb-8" style="background-image:url('/image/verify-bg.svg'); background-size:cover;">
                  <img :src="verificationStatus.verification.avatar_url || '/image/default-avatar.png'" class="w-20 h-20 rounded-full object-cover mr-6" />
                  <div class="flex-1">
                    <div class="flex items-center mb-1">
                      <span class="text-2xl font-bold text-[#222]">{{ verificationStatus.verification.full_name || '--' }}</span>
                    </div>
                    <div class="flex items-center text-base text-[#8D8D8D] font-medium">
                      <!-- <img src="/image/verified-badge.svg" class="w-5 h-5 mr-1" v-if="verificationStatus.verification_status === 'verified'" /> -->
                      {{ verificationStatus.verification.current_role || '--' }}
                    </div>
                    <div class="text-base text-[#8D8D8D] mt-1">{{ verificationStatus.verification.company_name || '--' }}</div>
                  </div>
                  <div class="absolute right-6 top-4 flex items-center bg-[#F6EEE9] rounded-xl px-4 py-1 border border-[#E2CFC2]">
                    <!-- <img src="/image/clock.svg" class="w-4 h-4 mr-1" /> -->
                    <span class="text-[#C47A5A] font-bold text-base">{{ verificationStatus.verification.verification_status  || '--' }}</span>
                  </div>
                </div>
                <div class="text-[#8D8D8D] text-lg mb-2">Submission Date: <span class="text-[#222] font-bold">{{ verificationStatus.verification.created_at || '--' }}</span></div>
                <div class="text-[#8D8D8D] text-lg mb-8">Status: <span class="text-[#222] font-bold">{{ verificationStatus.verification.verification_status || '--' }}</span></div>
                <div class="flex flex-col items-center gap-4">
                  <button class="flex items-center justify-center gap-2 border border-black rounded-full px-8 py-3 text-lg font-bold hover:bg-[#F6EEE9] transition-all mx-auto" style="min-width: 240px;" @click="openVerificationDetailModal">
                    <img src="/image/eye.png" class="w-5 h-5" />
                    View Details
                  </button>
                  <!-- <button v-if="!['verified', 'completed', 'success'].includes((verificationStatus.verification.verification_status || '').toLowerCase())"
                          class="flex items-center justify-center gap-2 border border-[#C47A5A] bg-[#C47A5A] text-white rounded-full px-8 py-3 text-lg font-bold hover:bg-[#a95e3d] transition-all mx-auto"
                          style="min-width: 240px;"
                          @click="onContinueVerification">
                    <img src="/image/user12.png" class="w-5 h-5" />
                    Continue Verification
                  </button> -->
                </div>
              </div>
            </template>
          </div>
        </template>
        <!-- <template v-else-if="localTab==='subscription'">
          <!-- 订阅内容区-视觉稿100%还原 -->
          <!-- <div class="subscription-card relative w-full h-full flex flex-col items-center justify-center rounded-3xl bg-white shadow-none border">
            <div class="absolute left-0 top-0 rounded-tl-2xl rounded-br-2xl bg-[#F8E6DD] text-[#C47A5A] text-lg font-bold px-8 py-3" style="border-top-left-radius: 20px; border-bottom-right-radius: 20px;">Current plan</div>
            <div class="mt-12 mb-6 flex flex-col items-center">
              <div class="rounded-full bg-[#C47A5A] text-white text-xl font-bold px-16 py-3 mb-8" style="min-width: 200px; text-align: center;">Free</div>
              <ul class="space-y-6">
                <li class="flex items-center text-[#3B4659] text-lg font-medium"><img src="/image/choose.png" class="mr-4" />Analyze Only Google Scholar</li>
                <li class="flex items-center text-[#3B4659] text-lg font-medium"><img src="/image/choose.png" class="mr-4" />Limited Credits (3/day)</li>
                <li class="flex items-center text-[#3B4659] text-lg font-medium"><img src="/image/choose.png" class="mr-4" />Slow Queue</li>
                <li class="flex items-center text-[#3B4659] text-lg font-medium"><img src="/image/choose.png" class="mr-4" />No Bilboard (View Only)</li>
              </ul>
            </div>
          </div>
        </template> -->
      </div>
    </div>
  </div>
  <EditNameModal v-if="showEditNameModal" :currentName="user?.display_name || firebaseUser?.display_name || ''" @close="showEditNameModal = false" @save="onSaveDisplayName" />
  <SocialConnectModal 
    v-if="showSocialConnectModal" 
    :platformType="currentPlatform" 
    :currentValue="getCurrentPlatformValue()" 
    @close="showSocialConnectModal = false" 
    @save="onSocialSave" 
    @unbind="onSocialUnbind" 
  />
  <VerificationTypeModal v-if="showVerificationTypeModal" @close="showVerificationTypeModal = false" />
  <VerificationFormModal v-if="showVerificationFormModal" @close="showVerificationFormModal = false" />
  <RecruiterVerificationFormModal v-if="showRecruiterVerificationFormModal" @close="showRecruiterVerificationFormModal = false" />
  <VerificationDetailModal v-if="showVerificationDetailModal" :verification="verificationStatus.verification" @close="showVerificationDetailModal = false" />
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { getCurrentUser, getCurrentFirebaseUser, getUserVerification, uploadFile, updateVerificationStatus, updateUserInfo } from '@/api/user'
import EditNameModal from '../EditNameModal/index.vue'
import SocialConnectModal from '../SocialConnectModal/index.vue'
import VerificationTypeModal from '../VerificationTypeModal/index.vue'
import VerificationFormModal from '../VerificationFormModal/index.vue'
import VerificationDetailModal from '../VerificationDetailModal/index.vue'

const props = defineProps({
  user: { type: Object, default: () => ({}) },
  tab: { type: String, default: 'settings' }
})

const emit = defineEmits(['close', 'signout'])
const { currentUser } = useFirebaseAuth()

const localTab = ref(props.tab)
watch(() => props.tab, (val) => { localTab.value = val })

const user = ref({});
const firebaseUser = ref({});
const showEditNameModal = ref(false)
const showSocialConnectModal = ref(false)
const currentPlatform = ref('')
const showVerificationTypeModal = ref(false)
const verificationStatus = ref(null)
const verificationLoading = ref(false)
const showVerificationFormModal = ref(false)
const showRecruiterVerificationFormModal = ref(false)
const avatarLoading = ref(false)
const showVerificationDetailModal = ref(false)
const isDark = ref(false)

onMounted(async () => {
  // 检测深色模式
  isDark.value = document.documentElement.classList.contains('dark')
  const observer = new MutationObserver(() => {
    isDark.value = document.documentElement.classList.contains('dark')
  })
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  
  // 原有的用户数据获取逻辑
  const uid = currentUser.value.uid
  const [res, firebaseRes] = await Promise.all([
    getCurrentUser({ Userid: uid }),
    getCurrentFirebaseUser({ Userid: uid })
  ])

  if (res.success) {
    user.value = res.user
  }
  if (firebaseRes.success) {
    firebaseUser.value = firebaseRes.firebase_user
  }
  if (localTab.value === 'verification') {
    fetchVerificationStatus()
  }
})

async function onAvatarChange(e) {
  if (!e.target.files || !e.target.files[0]) return
  avatarLoading.value = true
  const file = e.target.files[0]
  if (!file) {
    avatarLoading.value = false
    return
  }
  const formData = new FormData()
  formData.append('file', file)
  formData.append('folder', 'profile')
  const res = await uploadFile(formData, { Userid: currentUser.value.uid })
  console.log('onAvatarChange res', res)
  if (res.code === 200) {
    user.value.avatar_url = res.data.publicUrl;
    try {
      // Update user info with new avatar URL
      const updateRes = await updateUserInfo({
        profile_picture: res.data.publicUrl
      }, {
        Userid: currentUser.value.uid
      })
      console.log('updateUserInfo res', updateRes)
      // 更新成功后重新获取用户信息
      const [userRes, firebaseUserRes] = await Promise.all([
        getCurrentUser({ Userid: currentUser.value.uid }),
        getCurrentFirebaseUser({ Userid: currentUser.value.uid })
      ])

      if (userRes.success) {
        user.value = userRes.user
      }
      if (firebaseUserRes.success) {
        firebaseUser.value = firebaseUserRes.firebase_user
      }
      
      // 通知其他组件用户信息已更新
      const { $emitter } = useNuxtApp()
      $emitter.emit('user-profile-updated')
    } catch (error) {
      console.error('updateVerificationStatus error', error) 
    }
  }
  avatarLoading.value = false
  console.log('Selected avatar file:', file)
}

async function onSaveDisplayName(newName) {
  // TODO: 保存用户名逻辑
  showEditNameModal.value = false
  // 你可以在这里调用API并刷新用户信息
  const uid = currentUser.value.uid
  user.value = {}
  firebaseUser.value = {}
  const [res, firebaseRes] = await Promise.all([
    getCurrentUser({ Userid: uid}),
    getCurrentFirebaseUser({ Userid: uid })
  ])
  if (res.success) {
    user.value = res.user
  }
  if (firebaseRes.success) {
    firebaseUser.value = firebaseRes.firebase_user
  }
  
  // 通知其他组件用户信息已更新
  const { $emitter } = useNuxtApp()
  $emitter.emit('user-profile-updated')
  
  console.log('res', res.value)
  console.log('firebaseRes', firebaseRes.value)
}

function onConnect(type) {
  currentPlatform.value = type
  showSocialConnectModal.value = true
}

function getCurrentPlatformValue() {
  if (currentPlatform.value === 'twitter') {
    return user.value?.twitter_url || ''
  } else if (currentPlatform.value === 'github') {
    return user.value?.github_url || ''
  }
  return ''
}

async function onSocialSave(url) {
  try {
    const updateData = {}
    if (currentPlatform.value === 'twitter') {
      updateData.twitter_url = url
    } else if (currentPlatform.value === 'github') {
      updateData.github_url = url
    }

    const updateRes = await updateUserInfo(updateData, {
      Userid: currentUser.value.uid
    })
    
    if (updateRes) {
      const [userRes] = await Promise.all([
        getCurrentUser({ Userid: currentUser.value.uid })
      ])
      if (userRes.success) {
        user.value = userRes.user
      }
      // 通知其他组件用户信息已更新
      const { $emitter } = useNuxtApp()
      $emitter.emit('user-profile-updated')
      console.log(`${currentPlatform.value} account connected successfully!`)
    }
  } catch (error) {
    console.error(`Connect ${currentPlatform.value} error:`, error)
  }
  
  showSocialConnectModal.value = false
}

async function onSocialUnbind() {
  try {
    const updateData = {}
    if (currentPlatform.value === 'twitter') {
      updateData.twitter_url = ''
    } else if (currentPlatform.value === 'github') {
      updateData.github_url = ''
    }

    const updateRes = await updateUserInfo(updateData, {
      Userid: currentUser.value.uid
    })
    
    if (updateRes) {
      const [userRes] = await Promise.all([
        getCurrentUser({ Userid: currentUser.value.uid })
      ])
      if (userRes.success) {
        user.value = userRes.user
      }
      // 通知其他组件用户信息已更新
      const { $emitter } = useNuxtApp()
      $emitter.emit('user-profile-updated')
      console.log(`${currentPlatform.value} account unbound successfully!`)
    }
  } catch (error) {
    console.error(`Unbind ${currentPlatform.value} error:`, error)
  }
  
  showSocialConnectModal.value = false
}

function openVerificationTypeModal() {
  showVerificationTypeModal.value = true
  // 关闭当前弹窗
  setTimeout(() => {
    // 这里假设父组件控制弹窗关闭
    // $emit('close')
    // 如果需要，可以直接 emit('close')
    // 但通常建议父组件控制弹窗显示
  }, 0)
}

async function getUserVerificationStatus() {
  try {
    const res = await getUserVerification({Userid: currentUser.value.uid})
    return res.data;
  } catch (error) {
    console.error('getUserVerificationStatus error', error)
    return null
  }
}

async function fetchVerificationStatus() {
  verificationLoading.value = true
  verificationStatus.value = null
  try {
    verificationStatus.value = await getUserVerificationStatus();
    console.log('verificationStatus', verificationStatus.value)
  } catch (error) {
    console.error('fetchVerificationStatus error', error)
  }

  verificationLoading.value = false
}

watch(localTab, (val) => {
  if (val === 'verification') {
    fetchVerificationStatus()
  }
})

function onContinueVerification() {
  const type = verificationStatus.value?.verification?.user_type
  if (type === 'recruiter') {
    showRecruiterVerificationFormModal.value = true
  } else {
    showVerificationFormModal.value = true
  }
}

function openVerificationDetailModal() {
  showVerificationDetailModal.value = true
  setTimeout(() => {
    // $emit('close')
  }, 0)
}

function handleSignout() {
  emit('signout')
  emit('close') // 同时关闭设置弹窗
}
</script>

<style scoped>
.settings-modal {
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}
.iconImg {
  width: 50px;
  height: 50px;
}
.settings-tab-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.5rem 0;
  margin-bottom: 0.25rem;
  border-radius: 1rem;
  background: none;
  border: none;
  font-size: 1rem;
  color: #8D8D8D;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, transform 0.2s, font-weight 0.2s;
}
:root.dark .settings-tab-btn {
  color: #9CA3AF;
}
.settings-tab-btn.active {
  background: linear-gradient(90deg, #FFEBE3 0%, #FFFFFF00 100%);
  color: #222;
  transform: translateX(4px);
  font-weight: bold;
}
:root.dark .settings-tab-btn.active {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
  color: #F9FAFB;
  transform: translateX(4px);
  font-weight: bold;
}
.settings-tab-btn:hover:not(.active) {
  color: #222;
  transform: translateX(4px);
  font-weight: bold;
}
:root.dark .settings-tab-btn:hover:not(.active) {
  color: #F9FAFB;
  transform: translateX(4px);
  font-weight: bold;
}
.skeleton-title {
  width: 100px;
  height: 24px;
  border-radius: 6px;
  background: linear-gradient(90deg, #f3f3f3 25%, #ecebeb 50%, #f3f3f3 75%);
  animation: skeleton-loading 1.2s infinite linear;
}
:root.dark .skeleton-title {
  background: linear-gradient(90deg, #4B5563 25%, #374151 50%, #4B5563 75%);
}
.skeleton-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f3f3f3 25%, #ecebeb 50%, #f3f3f3 75%);
  animation: skeleton-loading 1.2s infinite linear;
}
:root.dark .skeleton-avatar {
  background: linear-gradient(90deg, #4B5563 25%, #374151 50%, #4B5563 75%);
}
.skeleton-line {
  height: 16px;
  border-radius: 6px;
  background: linear-gradient(90deg, #f3f3f3 25%, #ecebeb 50%, #f3f3f3 75%);
  animation: skeleton-loading 1.2s infinite linear;
}
:root.dark .skeleton-line {
  background: linear-gradient(90deg, #4B5563 25%, #374151 50%, #4B5563 75%);
}
.skeleton-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(90deg, #f3f3f3 25%, #ecebeb 50%, #f3f3f3 75%);
  animation: skeleton-loading 1.2s infinite linear;
}
:root.dark .skeleton-icon {
  background: linear-gradient(90deg, #4B5563 25%, #374151 50%, #4B5563 75%);
}
.skeleton-btn {
  width: 100%;
  height: 40px;
  border-radius: 999px;
  background: linear-gradient(90deg, #f3f3f3 25%, #ecebeb 50%, #f3f3f3 75%);
  animation: skeleton-loading 1.2s infinite linear;
}
:root.dark .skeleton-btn {
  background: linear-gradient(90deg, #4B5563 25%, #374151 50%, #4B5563 75%);
}
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}
.group:hover .avatar-upload-bg {
  opacity: 1;
}
.avatar-gradient-mask {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(to top, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.0) 60%);
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.subscription-card {
  background: #fff;
  border-radius: 32px;
  box-shadow: none;
  min-height: 100%;
  position: relative;
}
.check-icon {
  display: inline-block;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #F8E6DD;
  position: relative;
}
.check-icon::before {
  content: '';
  position: absolute;
  left: 7px;
  top: 7px;
  width: 14px;
  height: 14px;
  background: url('/image/check.svg') no-repeat center center / contain;
}
.connect-btn {
  border-radius: 18px;
  background: #fff;
  border: 1px solid #222;
  color: #222;
  font-size: 14px;
  font-weight: 600;
  padding: 0;
  transition: background 0.2s, color 0.2s, border 0.2s;
  width: 130px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
:root.dark .connect-btn {
  background: #212121;
  border: 1px solid #2E2E2E;
  color: #fff;
}
.connect-btn:hover {
  background: #F7F8FA;
  color: #222;
  border: 1px solid #222;
}
:root.dark .connect-btn:hover {
  background: #2E2E2E;
  color: #fff;
  border: 1px solid #2E2E2E;
}
.verification-btn {
  border-radius: 999px;
  background: #C47A5A;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  padding: 0.75rem 3rem;
  margin-top: 2rem;
  transition: all 0.2s;
  box-shadow: 0 2px 8px 0 rgba(196, 122, 90, 0.08);
  border: 1px solid #C47A5A;
  position: relative;
}
:root.dark .verification-btn {
  border: 1px solid #FAF9F5;
}
.verification-btn:not(:disabled):hover {
  background: #a95e3d;
}
.verification-li {
  list-style: disc inside;
  color: #757575;
  font-size: 1.18rem;
  margin-bottom: 0.75rem;
  margin-left: 0.5rem;
}
.signout-btn {
  background: transparent;
  transition: background 0.2s;
}
:root.dark .signout-btn {
  background: #212121;
  border: 1px solid #2E2E2E;
}
.signout-btn:hover {
  background: #F7F8FA;
}
:root.dark .signout-btn:hover {
  background: #2E2E2E;
}
</style> 