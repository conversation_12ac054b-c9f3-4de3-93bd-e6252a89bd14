<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 px-4"
      @click.self="close"
    >
      <transition name="scale">
        <div class="bg-white dark:bg-[#141415] rounded-5 shadow-xl p-5 md:p-5 p-4 w-106 max-w-full text-center rel" v-show="visible">
          <h2 class="text-7 md:text-7 text-5 font-bold mb-8 md:mb-8 mb-6 mt-5 md:mt-5 mt-3 text-black dark:text-white">Login</h2>
          <div class="space-y-5 md:space-y-5 space-y-4 px-5 md:px-5 px-2 mb-4">
            <button
              @click="handleAuth('google')"
              class="auth-btn bg-white dark:bg-[#141415] border border-gray-1500 dark:border-gray-600 hover:bg-black/5 dark:hover:bg-gray-700"
              :disabled="loading && currentProvider === 'google'"
            >
              <div
                v-if="loading && currentProvider === 'google'"
                class="i-svg-spinners:180-ring wh-6 md:wh-6 w-5 h-5"
              ></div>
              <div v-else class="i-devicon:google wh-6 md:wh-6 w-5 h-5"></div>
              <span class="text-black dark:text-white text-lg md:text-lg text-base">
                <span class="hidden md:inline">Continue with Google</span>
                <span class="md:hidden">Google</span>
              </span>
            </button>

            <!-- <button 
              @click="handleAuth('twitter')" 
              class="auth-btn bg-black hover:bg-black/80"
              :disabled="loading && currentProvider === 'twitter'"
            >
              <div v-if="loading && currentProvider === 'twitter'" class="i-svg-spinners:180-ring wh-6 text-white"></div>
              <div v-else class="i-proicons:x-twitter wh-6 text-white"></div>
              <span class="text-white">Continue with twitter</span>
            </button> -->
          </div>
          
          <!-- 添加用户协议提示 -->
          <div class="text-center text-xs md:text-sm text-gray-500 dark:text-gray-400 px-5 md:px-5 px-2 mb-4">
            Login, you agree to our 
            <NuxtLink to="/terms" class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 hover:underline" @click="close">Terms of Service</NuxtLink>
            and 
            <NuxtLink to="/privacy" class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 hover:underline" @click="close">Privacy Policy</NuxtLink>.
          </div>
          
          <div class="abs top-5 right-5">
            <div
              class="wh-6 text-#333 i-material-symbols:close cursor-pointer opacity-32 dark:text-white"
              @click="close"
            ></div>
          </div>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script setup lang="ts">
  import { defineProps, defineEmits, ref, defineExpose } from 'vue'

  const props = defineProps<{
    visible: boolean
    loading?: boolean
    provider?: 'google' | 'twitter' | null
  }>()

  const emit = defineEmits<{
    (e: 'update:visible', val: boolean): void
    (e: 'auth', provider: 'google' | 'twitter'): void
  }>()

  const currentProvider = ref<'google' | 'twitter' | null>(null)

  const close = () => {
    emit('update:visible', false)
    resetLoading()
  }

  const handleAuth = (provider: 'google' | 'twitter') => {
    currentProvider.value = provider
    emit('auth', provider)
  }

  const resetLoading = () => {
    currentProvider.value = null
  }

  defineExpose({
    resetLoading,
  })
</script>

<style scoped>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .scale-enter-active,
  .scale-leave-active {
    transition: transform 0.3s ease;
  }

  .scale-enter-from,
  .scale-leave-to {
    transform: scale(0.8);
  }

  .auth-btn {
    @apply rounded-10px w-full f-cer gap-2.5 md:gap-2.5 gap-2 py-3 md:py-3 py-4 font-medium text-lg md:text-lg text-base transition-colors;
  }

  .auth-btn:disabled {
    @apply opacity-70 cursor-not-allowed;
  }

  /* 移动端优化 */
  @media (max-width: 640px) {
    .auth-btn {
      @apply py-4 text-base gap-2;
    }
    
    .rounded-5 {
      border-radius: 1.25rem;
    }
  }
</style>
