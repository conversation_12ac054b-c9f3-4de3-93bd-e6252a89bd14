<template>
  <div>
    <div v-if="displayCandidates.length > 0" class="talent-card-container">
      <div class="results-card-bg2"></div>
      <div class="results-card-bg"></div>

      <!-- 导航按钮 -->
      <div
        class="nav-button prev-button"
        @click="prevCandidate"
        :class="{ disabled: currentIndex === 0, hidden: showNetworkModal }"
      >
        <svg
          width="30"
          height="30"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10 12L6 8L10 4"
            stroke="currentColor"
            stroke-width="1.2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div
        class="nav-button next-button"
        @click="nextCandidate"
        :class="{
          disabled: currentIndex >= displayCandidates.length - 1,
          hidden: showNetworkModal,
        }"
      >
        <svg
          width="30"
          height="30"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6 12L10 8L6 4"
            stroke="currentColor"
            stroke-width="1.2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>

      <!-- 主卡片 -->
      <Transition>
        <div>
          <div
            :class="{ animate__fadeInLeft: !operator, animate__fadeInRight: operator }"
            class="results-card animate__animated animate__faster"
            style="animation-duration: 0.4s" 
            :key="currentDisplayCandidate?.author_ids"
          >
            <div v-if="currentDisplayCandidate" class="card-content relative">
              <div class="fx-cer justify-center gap-4 absolute top-4 right-4 top-operator-btn">
                <div class="fx-cer gap-4">
                  <button
                    class="w-10 h-10 border-1 border-black rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
                    @click="() => {}"
                  >
                    <img
                      src="~/assets/image/id-card 3.svg"
                      alt="Refresh"
                      class="btn-icon btn-icon-light"
                    />
                    <img
                      src="~/assets/image/id-card-dark.svg"
                      alt="Refresh"
                      class="btn-icon btn-icon-dark"
                    />
                  </button>
                  <!-- email -->
                  <button
                    class="w-10 h-10 border-1 border-black rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
                    @click="openEmailModal(getProfileId(currentDisplayCandidate))"
                  >
                    <img
                      src="~/assets/image/email2.svg"
                      alt="Network"
                      class="btn-icon btn-icon-dark"
                    />
                    <img
                      src="~/assets/image/email.svg"
                      alt="Network"
                      class="btn-icon btn-icon-light"
                    />
                  </button>
                </div>
                <!-- favorites -->
                <button
                  class="w-10 h-10 border-1 border-black rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
                  @click="toggleFavorite"
                >
                  <img
                    :src="isFavorited ? starFill2 : starEmpty2"
                    alt="favorites"
                    class="btn-icon btn-icon-dark"
                  />
                  <img
                    :src="isFavorited ? starFill : starEmpty"
                    alt="favorites"
                    class="btn-icon btn-icon-light"
                  />
                </button>
              </div>
              <!-- 1. 头部区域 - 个人基本信息 -->
              <section class="header-section">
                <div class="personal-info">
                  <div class="avatar-container">
                    <img
                      :src="currentDisplayCandidate.avatarUrl || '/image/avator.png'"
                      :alt="currentDisplayCandidate.name"
                      class="avatar-image"
                      @error="handleImageError"
                    />
                  </div>
                  <div class="basic-info">
                    <h2 class="candidate-name">{{ currentDisplayCandidate.name }}</h2>
                    <p class="candidate-title">
                      <img src="~/assets/svg/verified.svg" alt="" />
                      <span>{{ currentDisplayCandidate.positionTitle }}</span>
                    </p>
                  </div>
                </div>
                <div class="skills-tags">
                  <span
                    v-for="skill in currentDisplayCandidate.skills.slice(0, 3)"
                    :key="skill"
                    class="skill-tag"
                  >
                    {{ skill }}
                  </span>
                </div>
              </section>

              <!-- Profile Section -->
              <section class="profile-section">
                <div
                  v-html="
                    currentDisplayCandidate.summary || 'Experienced researcher and practitioner in artificial intelligence. Focused on advancing the field through innovative research and practical applications.'
                  "
                ></div>
              </section>

              <!-- 2. 研究成果区域 -->
              <section v-if="currentDisplayCandidate.featuredWork" class="research-section">
                <h3 class="research-title">
                  <!-- 如果是GitHub仓库，添加链接 -->
                  <a
                    v-if="isGitHubRepository(currentDisplayCandidate)"
                    :href="getGitHubRepoUrl(currentDisplayCandidate)"
                    target="_blank"
                    class="hover:text-[#CB7C5D] hover:underline transition-colors cursor-pointer"
                  >
                    {{ getDisplayTitle(currentDisplayCandidate.featuredWork.title) }}
                  </a>
                  <!-- 否则显示普通文本 -->
                  <span v-else>{{ currentDisplayCandidate.featuredWork.title }}</span>
                </h3>
                <div class="research-meta">
                  <div class="conference-info">
                    <span class="conference-name"
                      >{{ currentDisplayCandidate.featuredWork.venue?.toUpperCase() }}
                      {{ currentDisplayCandidate.featuredWork.year }}</span
                    >
                  </div>
                  <span class="paper-type">{{ currentDisplayCandidate.featuredWork.type }}</span>
                </div>
              </section>

              <!-- 3. 操作区域 -->
              <section class="recommendation-section">
                <div class="action-section">
                  <div class="fx-cer gap-4 flex-[2]">
                    <button class="btn-see-another" @click="() => {}">
                      <img
                        src="~/assets/image/cv1.svg"
                        alt="Refresh"
                        class="btn-icon btn-icon-light"
                      />
                      <img
                        src="~/assets/image/cv2.svg"
                        alt="Refresh"
                        class="btn-icon btn-icon-dark"
                      />
                      CV
                    </button>
                    <button
                      class="btn-network flex-1"
                      @click="showNetworkModal = true"
                      :disabled="!isNetworkButtonEnabled"
                      :class="{ 'btn-disabled': !isNetworkButtonEnabled }"
                    >
                      <img
                        src="~/assets/image/button-group.svg"
                        alt="Network"
                        class="btn-icon btn-icon-dark"
                      />
                      <img
                        src="~/assets/image/button-group2.svg"
                        alt="Network"
                        class="btn-icon btn-icon-light"
                      />
                      Network
                    </button>
                  </div>

                  <button class="btn-analyze" @click="handleAnalyzeAction">
                    <img
                      src="~/assets/image/analysis.svg"
                      alt="Analyze"
                      class="btn-icon btn-icon-dark"
                    />
                    <img
                      src="~/assets/image/analysis2.svg"
                      alt="Analyze"
                      class="btn-icon btn-icon-light"
                    />
                    Analyze
                  </button>
                </div>
              </section>
            </div>
            <div v-else class="empty-state">
              <p>No candidate data available for current selection.</p>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <div v-else class="empty-state">
      <EmptyCard />
    </div>

    <!-- Network模态框组件 -->
    <NetworkModal
      v-if="currentDisplayCandidate"
      :show="showNetworkModal"
      :currentCandidate="currentDisplayCandidate"
      @update:show="showNetworkModal = $event"
    />
    <!-- Notification -->
    <Notification ref="notifier" />
    <!-- Email模态框 -->
    <EmailModal v-model="showEmailModal" :user-id="currentUserId" />
    <!-- Analyze模态框 -->
    <AnalyzeModal
      v-if="currentDisplayCandidate"
      :show="showAnalyzeModal"
      :candidate="currentDisplayCandidate"
      @update:show="showAnalyzeModal = $event"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import 'animate.css'
  import type {
    NewRecommendPapersResponse,
    NewRecommendedPaper, // API type for an individual paper with author info
    GitHubData, // API type for GitHub data
    RecommendDataItem, // Wrapper type with data_type
    DisplayCandidate, // Our transformed type for the card
  } from '~/api/types' // Import from api types
  import Notification from './Notification.vue'
  import NetworkModal from './NetworkModal.vue'
  import starFill from '~/assets/image/star-fill.svg'
  import starEmpty from '~/assets/image/star.png'
  import starFill2 from '~/assets/image/star-fill2.svg'
  import starEmpty2 from '~/assets/image/Star-dark.svg'
  import EmptyCard from './EmptyCard.vue'
  import EmailModal from './EmailModal.vue'
  import AnalyzeModal from './AnalyzeModal.vue'

  // --- Router ---
  const router = useRouter()

  // 当前用户 ID
  const currentUserId = ref(null)
  const showEmailModal = ref(false)
  const isFavorited = ref(false)
  const notifier = ref()
  const showAnalyzeModal = ref(false)

  const handleAnalyzeAction = () => {
    if (currentDisplayCandidate.value) {
      showAnalyzeModal.value = true
    }
  }
  // 获取用户的profile ID，用于email API
  const getProfileId = (candidate: any) => {
    if (!candidate) return null

    // 优先使用profile.id
    if (candidate.profile?.id) {
      return candidate.profile.id
    }

    // 回退到其他ID字段
    return candidate.id || candidate.author_ids || null
  }

  // 打开弹窗方法
  const openEmailModal = userId => {
    currentUserId.value = userId
    showEmailModal.value = true
  }

  const notify = (message: string) => {
    notifier.value?.add(message)
  }

  // 切换收藏状态
  const toggleFavorite = () => {
    isFavorited.value = !isFavorited.value
    notify(isFavorited.value ? 'Candidate saved successfully' : 'Candidate has been removed')
  }

  // --- Props ---
  const props = defineProps<{
    candidates?: DisplayCandidate[]
  }>()

  // --- Component State ---
  const rawApiResponse = ref<NewRecommendPapersResponse | null>(null)
  const loading = ref(true)
  const error = ref<string | null>(null)
  const currentIndex = ref(0)
  const operator = ref(0)
  const showNetworkModal = ref(false)

  // --- Helper Functions ---
  // 格式化数字，添加千分分隔符
  const formatNumber = (num: number | string): string => {
    if (typeof num === 'string') {
      const parsedNum = parseInt(num, 10)
      return isNaN(parsedNum) ? num : parsedNum.toLocaleString()
    }
    return num.toLocaleString()
  }

  const displayCandidates = computed((): DisplayCandidate[] => {
    // 如果有传入的candidates，优先使用
    if (props.candidates && props.candidates.length > 0) {
      return props.candidates
    }

    // 否则使用API响应数据
    if (!rawApiResponse.value || !rawApiResponse.value.data) {
      return []
    }
    return rawApiResponse.value.data
      .map((item: RecommendDataItem): DisplayCandidate => {
        // 根据data_type处理不同类型的数据 (paper 和 github)
        if (item.data_type === 'github') {
          const githubData = item.data as GitHubData

          // Handle recommend_reason for GitHub data
          let recommendationsArray: string[]
          if (Array.isArray(githubData.recommend_reason)) {
            recommendationsArray = githubData.recommend_reason.filter((reason: string) =>
              reason.trim()
            )
          } else if (
            typeof githubData.recommend_reason === 'string' &&
            githubData.recommend_reason
          ) {
            const sentences = githubData.recommend_reason.match(/[^.!?]+[.!?]+/g)
            recommendationsArray = sentences
              ? sentences.map((s: string) => s.trim())
              : [githubData.recommend_reason.trim()]
            if (recommendationsArray.length === 0 && githubData.recommend_reason.trim()) {
              recommendationsArray = [githubData.recommend_reason.trim()]
            }
          } else {
            recommendationsArray = ['No specific recommendation reason provided.']
          }

          return {
            id: githubData.login,
            name: githubData.name || githubData.login,
            positionTitle: githubData.position || 'Developer',
            institution: '', // Institution info will come from SearchPage.vue data mapping
            avatarUrl: githubData.avatar_url,
            skills: Array.isArray(githubData.tags) ? githubData.tags : [],
            summary: githubData.bio || '', // 使用GitHub的bio作为summary
            featuredWork: {
              title: (() => {
                const rawTitle = githubData.repository?.name || 'Featured Repository'
                return rawTitle.startsWith('/') ? rawTitle.substring(1) : rawTitle
              })(),
              venue: githubData.repository?.stars
                ? `⭐ ${formatNumber(githubData.repository.stars)} stars`
                : 'GitHub',
              type: 'GitHub Repository',
              year: '', // GitHub 不显示年份，避免显示 "GITHUB 2025"
            },
            recommendations: recommendationsArray,
            matchScore: githubData.score,
            author_ids: githubData.login, // 使用GitHub用户名作为ID
          }
        } else {
          // 处理paper类型的数据
          const apiPaper = item.data as NewRecommendedPaper
          const authorInfo = apiPaper.author_info

          // Handle recommend_reason for paper data
          let recommendationsArray: string[]
          if (Array.isArray(authorInfo.recommend_reason)) {
            recommendationsArray = authorInfo.recommend_reason.filter((reason: string) =>
              reason.trim()
            )
          } else if (
            typeof authorInfo.recommend_reason === 'string' &&
            authorInfo.recommend_reason
          ) {
            const sentences = authorInfo.recommend_reason.match(/[^.!?]+[.!?]+/g)
            recommendationsArray = sentences
              ? sentences.map((s: string) => s.trim())
              : [authorInfo.recommend_reason.trim()]
            if (recommendationsArray.length === 0 && authorInfo.recommend_reason.trim()) {
              recommendationsArray = [authorInfo.recommend_reason.trim()]
            }
          } else {
            recommendationsArray = ['No specific recommendation reason provided.']
          }

          return {
            id: authorInfo.author_id,
            name: authorInfo.author,
            positionTitle: authorInfo.position || 'Researcher',
            institution: '', // 不显示机构信息
            avatarUrl: authorInfo.avatar_url,
            skills: Array.isArray(apiPaper.tags) ? apiPaper.tags : [],
            summary: '', // paper类型暂时没有summary信息
            featuredWork: {
              title: apiPaper.title,
              venue: apiPaper.source,
              type: apiPaper.status,
              year: apiPaper.year,
            },
            recommendations: recommendationsArray,
            matchScore: authorInfo.score,
            author_ids: authorInfo.author_id,
          }
        }
      })
      .sort((a, b) => {
        // 按照matchScore降序排列（分数高的在前）
        if (b.matchScore !== a.matchScore) {
          return b.matchScore - a.matchScore
        }
        // 如果分数相同，按名字字母顺序排列
        return a.name.localeCompare(b.name)
      })
  })

  const currentDisplayCandidate = computed<DisplayCandidate | null>(() => {
    return displayCandidates.value[currentIndex.value] || null
  })

  // 判断Network按钮是否可用
  const isNetworkButtonEnabled = computed(() => {
    const candidate = currentDisplayCandidate.value
    if (!candidate) return false

    // 使用类型断言来访问额外的字段
    const candidateWithExtras = candidate as any

    // 检查新的search API数据结构
    if (candidateWithExtras.profile) {
      // 使用dataset字段，向后兼容builder字段
      const dataset = candidateWithExtras.dataset || candidateWithExtras.builder
      if (dataset === 'github') {
        // GitHub用户需要有profile.id字段
        return !!(candidateWithExtras.profile.id)
      } else if (dataset === 'scholar') {
        if (candidateWithExtras.group === 'company') {
          // 公司类型需要有id字段
          return !!(candidateWithExtras.profile.id)
        } else {
          // Scholar用户需要有openreview字段
          return !!(candidateWithExtras.profile.openreview)
        }
      }
    }

    // 兼容旧的数据结构
    if (candidate.author_ids) {
      return !!(candidate.author_ids)
    }

    return false
  })



  // --- Navigation ---
  const prevCandidate = () => {
    if (currentIndex.value > 0) {
      currentIndex.value--
      operator.value = 0
    }
  }

  const nextCandidate = () => {
    if (currentIndex.value < displayCandidates.value.length - 1) {
      currentIndex.value++
      operator.value = 1
    } else {
      console.log('At the end of current batch.')
    }
  }

  // --- Image Error Handling ---
  const handleImageError = (event: Event) => {
    const imgElement = event.target as HTMLImageElement
    console.log('handle img error')
    imgElement.src = '/image/avator.png'
  }

  // --- GitHub Repository Helper Functions ---
  const isGitHubRepository = (candidate: DisplayCandidate): boolean => {
    // 检查type是否为GitHub Repository或者venue包含stars（GitHub特征）
    return (
      candidate.featuredWork?.type === 'GitHub Repository' ||
      (candidate.featuredWork?.venue?.includes('stars') ?? false)
    )
  }

  const getGitHubRepoUrl = (candidate: DisplayCandidate): string => {
    // 仓库名已经包含用户名，格式为 username/repository-name
    const repoFullName = candidate.featuredWork?.title || ''

    // 确保仓库全名存在
    if (!repoFullName) {
      return '#'
    }

    return `https://github.com/${repoFullName}`
  }

  const getDisplayTitle = (title: string): string => {
    // 如果标题以斜杠开头，去掉开头的斜杠
    return title.startsWith('/') ? title.substring(1) : title
  }

  // Watch for external changes if this component were to receive candidates as props
  watch(
    () => props.candidates,
    newCandidates => {
      if (newCandidates && newCandidates.length > 0) {
        currentIndex.value = 0
        loading.value = false
        error.value = null
      }
    },
    { deep: true }
  )
</script>

<style scoped>
  /* 原始样式略，保留核心结构不变 */
  .talent-card-container {
    position: relative;
    max-width: 756px;
    width: 100%;
    height: auto;
    margin: 20px auto 0;
    padding: 0 16px;
  }

  .results-card {
    position: relative;
    width: 100%;
    min-height: 548px;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    backdrop-filter: blur(34px);
    box-shadow: 0px 4px 8px 0px #00000008;
    z-index: 2;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .results-card-bg,
  .results-card-bg2 {
    display: none; /* 移动端默认不展示装饰层 */
  }

  .card-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 16px;
    height: 100%;
  }

  /* 导航按钮 */
  .nav-button {
    position: absolute;
    width: 48px;
    height: 48px;
    background-color: #ffece5;
    border: 0.96px solid #f8ddd2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #cb7c5d;
    cursor: pointer;
    z-index: 25;
    transition: all 0.3s ease;
  }
  .nav-button:hover:not(.disabled) {
    background-color: #cb7c5d;
    border-color: #cb7c5d;
    color: #ffffff;
  }
  .nav-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  .nav-button.hidden {
    display: none;
  }
  .prev-button {
    top: calc(548px / 2 - 24px);
    left: -72px;
  }
  .next-button {
    top: calc(548px / 2 - 24px);
    right: -72px;
  }

  /* 头部区域 */
  .header-section {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .personal-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }
  .avatar-container {
    flex-shrink: 0;
  }
  .avatar-image {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    border: 1.2px solid #ffffff;
    object-fit: cover;
  }
  .dark .avatar-image {
    border: 1.2px solid #27282d;
  }
  .basic-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .candidate-name {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 16px;
    color: #000;
  }
  .candidate-title {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    color: #3d3d3d;
  }
  .skills-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  .skill-tag {
    min-width: 60px;
    height: 28px;
    padding: 6px 10px;
    background-color: #fbeae3;
    border: 0.5px solid #ebd2c9;
    border-radius: 4px;
    font-size: 12px;
    color: #cb7c5d;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Profile */
  .profile-section {
    padding: 16px;
    box-sizing: border-box;
  }

  /* Research */
  .research-section {
    background-color: #f8f4f3b2;
    padding: 16px;
    border-radius: 8px;
  }
  .research-title {
    font-weight: 600;
    font-size: 16px;
    color: #000;
    max-height: 72px;
    overflow: hidden;
  }
  .research-meta {
    padding-top: 12px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  .conference-name,
  .paper-type {
    height: 28px;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
    white-space: nowrap;
  }
  .conference-name {
    background-color: #e8e2ef;
    border: 0.5px solid #d0bee1;
    color: #68448b;
  }
  .paper-type {
    background-color: #fbeae3;
    border: 0.5px solid #ebd2c9;
    color: #cb7c5d;
  }

  /* 操作区域 */
  .recommendation-section {
    width: 100%;
    background-color: rgba(250, 247, 246, 0.5);
    border-radius: 8px;
    padding: 20px;
    box-sizing: border-box;
  }
  .action-section {
    display: flex;
    gap: 16px;
    justify-content: space-around;
    align-items: center;
  }
  .btn-analyze,
  .btn-network,
  .btn-see-another {
    flex: 1;
    height: 40px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: transparent;
    color: #000;
    border: 1px solid #000;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  .btn-see-another:hover {
    background-color: #f5f5f5;
  }
  .btn-analyze:hover,
  .btn-network:hover {
    background-color: #ccc;
  }
  .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f5f5f5 !important;
    color: #999 !important;
  }
  .btn-disabled:hover {
    background-color: #f5f5f5 !important;
  }
  .btn-icon {
    width: 24px;
    height: 24px;
  }

  /* Empty State */
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    color: #999;
  }

  /* 黑暗模式 */
  .dark .results-card {
    background: #141415;
    border: 1px solid #27282d;
    box-shadow: 0px 3px 8px 0px #0000001a;
  }
  .dark .candidate-name {
    color: #faf9f5;
  }
  .dark .candidate-title {
    color: #7a7a7a;
  }
  .dark .skill-tag {
    background-color: #323232;
    border: 0.5px solid #3e3e3e;
    color: #c6c6c6;
  }
  .dark .research-section {
    background-color: #292929;
  }
  .dark .research-title {
    color: #faf9f5;
  }
  .dark .paper-type {
    background-color: #323232;
    border: 0.5px solid #3e3e3e;
    color: #c6c6c6;
  }
  .dark .conference-name {
    background-color: #423c47;
    border: 0.5px solid #554b5e;
    color: #a191b0;
  }
  .dark .recommendation-section {
    background-color: #292929;
  }
  .dark .btn-see-another,
  .dark .btn-analyze,
  .dark .btn-network {
    border: 1px solid #323232;
    color: #faf9f5;
  }
  .dark .btn-see-another:hover,
  .dark .btn-analyze:hover,
  .dark .btn-network:hover {
    background-color: rgba(50, 50, 50, 0.1);
  }
  .dark .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #2a2a2a !important;
    color: #666 !important;
    border-color: #444 !important;
  }
  .dark .btn-disabled:hover {
    background-color: #2a2a2a !important;
  }
  .dark .nav-button {
    background-color: #292929;
    border: 0.96px solid #3e3e3e;
    color: #faf9f5;
  }
  .dark .nav-button:hover:not(.disabled) {
    background-color: #cb7c5d;
    border-color: #cb7c5d;
    color: #ffffff;
  }

  /* 图标切换 */
  .btn-icon-dark {
    display: none;
  }
  .dark .btn-icon-light {
    display: none;
  }
  .dark .btn-icon-dark {
    display: inline-block;
  }

  /* --- 响应式适配 --- */
  @media (max-width: 768px) {
    .talent-card-container {
      padding: 0 12px;
    }

    .avatar-image {
      width: 50px;
      height: 50px;
    }

    .recommendation-section,
    .research-section,
    .profile-section {
      padding: 8px;
    }

    .top-operator-btn {
      display: flex;
      justify-content: space-between;
      width: 90%;
    }

    .header-section {
      margin-top: 32px;
      padding: 8px;
      gap: 8px;
    }

    .results-card {
      padding: 16px;
    }

    .action-section {
      flex-direction: column;
      gap: 12px;
    }

    .action-section > div {
      width: 100%;
      display: flex;
      justify-content: space-between;
      gap: 16px;
      align-items: center;
    }

    .action-section div button {
      flex: 1;
    }

    .nav-button.prev-button,
    .nav-button.next-button {
      position: fixed;
      top: 40%;
      transform: none;
    }

    .nav-button.prev-button {
      left: 2%;
    }

    .nav-button.next-button {
      right: 2%;
    }

    .results-card-bg,
    .results-card-bg2 {
      display: none;
    }
  }

  @media (max-width: 640px) {
    .personal-info {
      flex-direction: row;
      align-items: flex-start;
      flex-wrap: nowrap;
      align-items: center;
      margin-top: 28px;
    }

    .candidate-name,
    .candidate-title,
    .skills-tags {
      position: static;
    }

    .skills-tags {
      margin-top: 8px;
      max-width: 100%;
    }

    .card-content {
      gap: 2px;
    }

    .recommendation-section,
    .research-section,
    .profile-section {
      padding: 8px;
    }

    .btn-analyze,
    .btn-network,
    .btn-see-another {
      font-size: 13px;
      height: 40px;
      width: 100%;
    }

    .btn-analyze {
      line-height: 38px;
    }
  }

  /* 后端返回的summary高亮样式 */
  .profile-section :deep(.talent-position) {
    background-color: #E57979;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
  }

  .profile-section :deep(.talent-company) {
    background-color: #E57979;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
  }

  .profile-section :deep(.talent-research) {
    background-color: #4989F8;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
  }

  /* 暗色模式下的高亮样式 */
  .dark .profile-section :deep(.talent-position) {
    background-color: #42E287;
    color: black;
  }

  .dark .profile-section :deep(.talent-company) {
    background-color: #42E287;
    color: black;
  }

  .dark .profile-section :deep(.talent-research) {
    background-color: #F8C749;
    color: black;
  }
</style>
