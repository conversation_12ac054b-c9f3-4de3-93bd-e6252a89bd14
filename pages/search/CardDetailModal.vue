<template>
  <Transition name="modal">
    <div
      v-if="modelValue"
      class="fixed inset-0 z-50 bg-black bg-opacity-40 flex justify-center items-end md:items-center"
      @click.self="$emit('update:modelValue', false)"
    >
      <!-- 主体弹窗 -->
      <div
        class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white relative p-4 shadow-lg w-full md:w-[320px] md:mx-0 fixed md:relative bottom-0 md:bottom-auto rounded-t-xl md:rounded-xl transition-transform duration-300 md:rounded-xl rounded-b-none md:px-4"
      >
        <!-- 关闭按钮 -->
        <button
          class="absolute top-2 right-2 text-gray-500 bg-transparent hover:text-gray-700 dark:hover:text-gray-300"
          @click="$emit('update:modelValue', false)"
        >
          <img src="~/assets/image/Close-small.svg" alt="" />
        </button>

        <!-- 顶部用户信息 -->
        <div class="flex items-center gap-3 mb-4">
          <img :src="user.avatar" alt="avatar" class="w-12 h-12 rounded-full" />
          <div class="text-left">
            <p class="text-base font-semibold">{{ user.name }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.title }}</p>
          </div>
        </div>

        <!-- 下方按钮区域（竖排） -->
        <div class="flex flex-col gap-3">
          <button
            class="btn-analyze"
            @click="handleGitHubAnalyze"
            :disabled="!isGitHubAnalyzeEnabled"
            :class="{ 'btn-disabled': !isGitHubAnalyzeEnabled }"
          >
            <img src="~/assets/image/analysis.svg" alt="GitHub Analyze" class="btn-icon btn-icon-dark" />
            <img src="~/assets/image/analysis2.svg" alt="GitHub Analyze" class="btn-icon btn-icon-light" />
            GitHub Analyze
          </button>

          <button
            class="btn-analyze"
            @click="handleScholarAnalyze"
            :disabled="!isScholarAnalyzeEnabled"
            :class="{ 'btn-disabled': !isScholarAnalyzeEnabled }"
          >
            <img src="~/assets/image/analysis.svg" alt="Scholar Analyze" class="btn-icon btn-icon-dark" />
            <img src="~/assets/image/analysis2.svg" alt="Scholar Analyze" class="btn-icon btn-icon-light" />
            Scholar Analyze
          </button>

          <button
            class="btn-network"
            @click="showNetworkModal = true"
            :disabled="!isNetworkButtonEnabled"
            :class="{ 'btn-disabled': !isNetworkButtonEnabled }"
          >
            <img
              src="~/assets/image/button-group.svg"
              alt="Network"
              class="btn-icon btn-icon-dark"
            />
            <img
              src="~/assets/image/button-group2.svg"
              alt="Network"
              class="btn-icon btn-icon-light"
            />
            Network
          </button>
          <button class="btn-see-another">
            <img src="~/assets/image/cv1.svg" alt="Refresh" class="btn-icon btn-icon-light" />
            <img src="~/assets/image/refresh2.svg" alt="Refresh" class="btn-icon btn-icon-dark" />
            Resume/CV
          </button>
        </div>
      </div>
      <!-- Network模态框组件 -->
      <NetworkModal
        v-if="props.user"
        :show="showNetworkModal"
        :currentCandidate="props.user"
        @update:show="showNetworkModal = $event"
      />
    </div>
  </Transition>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import NetworkModal from '@/components/SearchCard/NetworkModal.vue'
  import { useRouter } from 'vue-router'
  const router = useRouter()

  const props = defineProps({
    modelValue: Boolean,
    user: {
      type: Object,
      required: true,
    },
  })

  const showNetworkModal = ref(false)

  const emit = defineEmits(['update:modelValue'])

  // 判断Network按钮是否可用
  const isNetworkButtonEnabled = computed(() => {
    const candidate = props.user
    if (!candidate) return false

    // 检查新的search API数据结构
    if (candidate.profile) {
      // 使用dataset字段，向后兼容builder字段
      const dataset = candidate.dataset || candidate.builder
      if (dataset === 'github') {
        // GitHub用户需要有profile.id字段
        return !!(candidate.profile.id)
      } else if (dataset === 'scholar') {
        if (candidate.group === 'company') {
          // 公司类型需要有id字段
          return !!(candidate.profile.id)
        } else {
          // Scholar用户需要有openreview字段
          return !!(candidate.profile.openreview)
        }
      }
    }

    // 兼容旧的数据结构
    if (candidate.author_ids) {
      return !!(candidate.author_ids)
    }

    return false
  })

  // 计算按钮可用性
  const isGitHubAnalyzeEnabled = computed(() => {
    const enabled = !!(props.user?.profile?.github)
    console.log('GitHub analyze enabled:', enabled, 'github value:', props.user?.profile?.github)
    return enabled
  })

  const isScholarAnalyzeEnabled = computed(() => {
    const enabled = !!(props.user?.profile?.scholar)
    console.log('Scholar analyze enabled:', enabled, 'scholar value:', props.user?.profile?.scholar)
    return enabled
  })

  // --- Actions ---
  const handleGitHubAnalyze = () => {
    if (props.user?.profile?.github) {
      router.push(`/github?query=${props.user.profile.github}`)
    }
  }

  const handleScholarAnalyze = () => {
    if (props.user?.profile?.scholar) {
      router.push(`/report?query=${props.user.profile.scholar}`)
    }
  }
</script>

<style>
  /* 按钮图标控制 */
  .btn-icon {
    width: 24px;
    height: 24px;
  }

  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  .btn-analyze,
  .btn-network,
  .btn-see-another {
    height: 40px;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 100%;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
    box-sizing: border-box;
    border: 1px solid #000000;
  }

  .btn-analyze,
  .btn-network {
    background-color: transparent;
    color: #000000;
    /* padding: 16px 32px; -- width/height and flex handles this */
  }

  .btn-analyze:hover,
  .btn-network:hover {
    background-color: #ccc;
  }
  .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed !important;
    background-color: #f5f5f5 !important;
    color: #999 !important;
    border-color: #ddd !important;
    pointer-events: none;
  }
  .btn-disabled:hover {
    background-color: #f5f5f5 !important;
    color: #999 !important;
  }

  .btn-see-another {
    background-color: transparent;
    color: #000000;
    /* padding: 16px 20px; */
  }

  .btn-see-another:hover {
    background-color: #f5f5f5;
  }

  .dark .btn-see-another,
  .dark .btn-analyze,
  .dark .btn-network {
    background-color: transparent;
    border: 1px solid #323232;
    color: #faf9f5;
  }

  .dark .btn-see-another:hover,
  .dark .btn-analyze:hover,
  .dark .btn-network:hover {
    background-color: rgba(50, 50, 50, 0.1);
  }
  .dark .btn-disabled {
    opacity: 0.5;
    cursor: not-allowed !important;
    background-color: #2a2a2a !important;
    color: #666 !important;
    border-color: #444 !important;
    pointer-events: none;
  }
  .dark .btn-disabled:hover {
    background-color: #2a2a2a !important;
    color: #666 !important;
  }

  @media (max-width: 768px) {
    .modal-enter-active,
    .modal-leave-active {
      transition: all 0.3s ease;
    }

    .modal-enter-from {
      transform: translateY(100%);
      opacity: 0;
    }

    .modal-enter-to {
      transform: translateY(0);
      opacity: 1;
    }

    .modal-leave-from {
      transform: translateY(0);
      opacity: 1;
    }

    .modal-leave-to {
      transform: translateY(100%);
      opacity: 0;
    }
  }
</style>
