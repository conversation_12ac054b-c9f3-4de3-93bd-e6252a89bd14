<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-200">Search API Test Page</h1>
    
    <!-- Authentication Status -->
    <div class="mb-6 p-4 rounded-lg" :class="authStatusClass">
      <h2 class="text-lg font-semibold mb-2">Authentication Status</h2>
      <div v-if="!authInitialized" class="text-yellow-600">
        🔄 Initializing authentication...
      </div>
      <div v-else-if="currentUser" class="text-green-600">
        ✅ Authenticated as: {{ currentUser.email }}
        <br>
        <span class="text-sm text-gray-600">UID: {{ currentUser.uid }}</span>
      </div>
      <div v-else class="text-red-600">
        ❌ Not authenticated - Please login to test the API
      </div>
    </div>

    <!-- API Type Selection -->
    <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
      <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Select API to Test</h2>
      <div class="flex gap-4">
        <button
          @click="selectedApi = 'search'"
          :class="selectedApi === 'search' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'"
          class="px-4 py-2 rounded-lg font-medium transition-colors"
        >
          🔍 Search API
        </button>
        <button
          @click="selectedApi = 'network'"
          :class="selectedApi === 'network' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'"
          class="px-4 py-2 rounded-lg font-medium transition-colors"
        >
          🌐 Network API
        </button>
        <button
          @click="selectedApi = 'email'"
          :class="selectedApi === 'email' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'"
          class="px-4 py-2 rounded-lg font-medium transition-colors"
        >
          📧 Email API
        </button>
      </div>
    </div>

    <!-- Search API Test Form -->
    <div v-if="selectedApi === 'search'" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Search API Test Form</h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <!-- Search Category -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Category
          </label>
          <select
            v-model="selectedCategory"
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="type:scholar">Scholar Search</option>
            <option value="type:github">GitHub Search</option>
            <option value="group:company">Company Search</option>
          </select>
        </div>

        <!-- Search Term -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Term
          </label>
          <input
            v-model="searchTerm"
            type="text"
            placeholder="e.g., video, machine learning, etc."
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>

      <!-- Test Button -->
      <button
        @click="testSearchApi"
        :disabled="!currentUser || loading || !searchTerm.trim()"
        class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        <span v-if="loading">🔄 Testing API...</span>
        <span v-else>🚀 Test Search API</span>
      </button>

      <!-- Quick Test Examples -->
      <div class="mt-4">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Quick test examples:</p>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="example in quickExamples"
            :key="example.label"
            @click="setExample(example)"
            class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {{ example.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Network API Test Form -->
    <div v-if="selectedApi === 'network'" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Network API Test Form</h2>

      <div class="grid grid-cols-1 gap-4 mb-4">
        <!-- Network ID -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Network ID
          </label>
          <input
            v-model="networkId"
            type="text"
            placeholder="e.g., ezyang, ~Sam_Gao1, 177f510a-19a9-467d-bdcc-30efc497f70d"
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>

      <!-- Test Button -->
      <button
        @click="testNetworkApi"
        :disabled="!currentUser || loading || !networkId.trim()"
        class="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        <span v-if="loading">🔄 Testing API...</span>
        <span v-else>🌐 Test Network API</span>
      </button>

      <!-- Quick Test Examples -->
      <div class="mt-4">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Quick test examples:</p>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="example in networkExamples"
            :key="example.label"
            @click="setNetworkExample(example)"
            class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {{ example.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Email API Test Form -->
    <div v-if="selectedApi === 'email'" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Email API Test Form</h2>

      <!-- Profile ID Input -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Profile ID:
        </label>
        <div class="relative">
          <input
            v-model="profileId"
            type="text"
            placeholder="e.g., 05128c4d-2968-49be-a8b8-97550d272cef"
            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>

      <!-- Test Button -->
      <button
        @click="testEmailApi"
        :disabled="!currentUser || loading || !profileId.trim()"
        class="w-full bg-orange-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        <span v-if="loading">🔄 Testing API...</span>
        <span v-else>📧 Test Email API</span>
      </button>

      <!-- Quick Test Examples -->
      <div class="mt-4">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Quick test examples:</p>
        <div class="flex flex-wrap gap-2">
          <button
            @click="profileId = '05128c4d-2968-49be-a8b8-97550d272cef'"
            class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            Sample Profile ID
          </button>
        </div>
      </div>
    </div>

    <!-- API Response -->
    <div v-if="apiResponse || error" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">API Response</h2>
      
      <!-- Error Display -->
      <div v-if="error" class="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <h3 class="text-red-800 dark:text-red-400 font-semibold mb-2">❌ Error</h3>
        <pre class="text-red-700 dark:text-red-300 text-sm whitespace-pre-wrap">{{ error }}</pre>
      </div>

      <!-- Success Response -->
      <div v-if="apiResponse" class="mb-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <h3 class="text-green-800 dark:text-green-400 font-semibold mb-2">✅ Success</h3>
        <div class="text-sm text-green-700 dark:text-green-300 mb-2">
          Status: {{ lastRequestStatus }} | Response Time: {{ responseTime }}ms
        </div>
      </div>

      <!-- Raw Response Data -->
      <div v-if="apiResponse" class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 overflow-auto">
        <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">Raw Response:</h4>
        <pre class="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap max-h-96 overflow-y-auto">{{ JSON.stringify(apiResponse, null, 2) }}</pre>
      </div>
    </div>

    <!-- API Documentation -->
    <div class="mt-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
      <h2 class="text-lg font-semibold mb-4 text-blue-800 dark:text-blue-400">📚 API Documentation</h2>

      <!-- Search API Documentation -->
      <div v-if="selectedApi === 'search'" class="text-sm text-blue-700 dark:text-blue-300 space-y-2">
        <h3 class="font-semibold text-base mb-2">🔍 Search API</h3>
        <p><strong>Real API:</strong> GET http://search.dinq.io/api/v1/talent/search</p>
        <p><strong>Proxy Path:</strong> GET /api/v1/talent/search (CORS-enabled proxy)</p>
        <p><strong>Authentication:</strong> Bearer {uid} (from Firebase Auth)</p>
        <p><strong>Required Parameters:</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li><code>text</code>: {category} {search_term}</li>
          <li><code>code</code>: 64696E71 (fixed value)</li>
        </ul>
        <p><strong>Categories:</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li><code>type:scholar</code> - Scholar search</li>
          <li><code>type:github</code> - GitHub search</li>
          <li><code>group:company</code> - Company search</li>
        </ul>
      </div>

      <!-- Network API Documentation -->
      <div v-if="selectedApi === 'network'" class="text-sm text-blue-700 dark:text-blue-300 space-y-2">
        <h3 class="font-semibold text-base mb-2">🌐 Network API</h3>
        <p><strong>Real API:</strong> GET http://search.dinq.io/api/v1/talent/network</p>
        <p><strong>Proxy Path:</strong> GET /api/v1/talent/network (CORS-enabled proxy)</p>
        <p><strong>Authentication:</strong> Bearer {uid} (from Firebase Auth)</p>
        <p><strong>Required Parameters:</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li><code>network_id</code>: Network identifier (GitHub login, OpenReview ID, or company ID)</li>
        </ul>
        <p><strong>Example URLs:</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1 text-xs">
          <li><code>?network_id=ezyang</code></li>
          <li><code>?network_id=~Sam_Gao1</code></li>
          <li><code>?network_id=177f510a-19a9-467d-bdcc-30efc497f70d</code></li>
        </ul>
      </div>

      <!-- Email API Documentation -->
      <div v-if="selectedApi === 'email'" class="text-sm text-blue-700 dark:text-blue-300 space-y-2">
        <h3 class="font-semibold text-base mb-2">📧 Email API</h3>
        <p><strong>Real API:</strong> GET http://search.dinq.io/api/v1/talent/email</p>
        <p><strong>Proxy Path:</strong> GET /api/v1/talent/email (CORS-enabled proxy)</p>
        <p><strong>Authentication:</strong> Bearer {uid} (from Firebase Auth)</p>
        <p><strong>Required Parameters:</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1">
          <li><code>profile_id</code>: Profile identifier (UUID format)</li>
        </ul>
        <p><strong>Response Format:</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1 text-xs">
          <li><code>{"data": "<EMAIL>"}</code> - Success response with email</li>
          <li><code>{"error": "Error message"}</code> - Error response</li>
        </ul>
        <p><strong>Example URLs:</strong></p>
        <ul class="list-disc list-inside ml-4 space-y-1 text-xs">
          <li><code>?profile_id=05128c4d-2968-49be-a8b8-97550d272cef</code></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Firebase Auth
const { currentUser, authInitialized } = useFirebaseAuth()

// API selection
const selectedApi = ref('search')

// Form state
const selectedCategory = ref('type:scholar')
const searchTerm = ref('')
const loading = ref(false)

// Network API form state
const networkId = ref('')

// Email API form state
const profileId = ref('')

// Response state
const apiResponse = ref(null)
const error = ref('')
const lastRequestStatus = ref('')
const responseTime = ref(0)

// Quick test examples
const quickExamples = [
  { label: 'Scholar: video', category: 'type:scholar', term: 'video' },
  { label: 'GitHub: machine learning', category: 'type:github', term: 'machine learning' },
  { label: 'Company: google', category: 'group:company', term: 'google' },
  { label: 'Scholar: AI', category: 'type:scholar', term: 'artificial intelligence' },
]

// Network API examples
const networkExamples = [
  { label: 'GitHub: ezyang', id: 'ezyang' },
  { label: 'Scholar: ~Sam_Gao1', id: '~Sam_Gao1' },
  { label: 'Scholar Company: 177f510...', id: '177f510a-19a9-467d-bdcc-30efc497f70d' },
]

// Computed properties
const authStatusClass = computed(() => {
  if (!authInitialized.value) return 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
  if (currentUser.value) return 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
  return 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
})

// Methods
const setExample = (example: any) => {
  selectedCategory.value = example.category
  searchTerm.value = example.term
}

const setNetworkExample = (example: any) => {
  networkId.value = example.id
}

const testSearchApi = async () => {
  if (!currentUser.value || !searchTerm.value.trim()) return

  loading.value = true
  error.value = ''
  apiResponse.value = null
  
  const startTime = Date.now()

  try {
    const query = `${selectedCategory.value} ${searchTerm.value.trim()}`
    const url = new URL('/api/v1/talent/search', window.location.origin)
    url.searchParams.append('text', query)
    url.searchParams.append('code', '64696E71')

    console.log('Making API request:', {
      url: url.toString(),
      headers: {
        'Authorization': `Bearer ${currentUser.value.uid}`
      }
    })

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${currentUser.value.uid}`,
        'Content-Type': 'application/json',
      },
    })

    responseTime.value = Date.now() - startTime
    lastRequestStatus.value = `${response.status} ${response.statusText}`

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const data = await response.json()
    apiResponse.value = data
    
    console.log('API Response:', data)
    
  } catch (err: any) {
    console.error('API Error:', err)
    error.value = err.message || 'Unknown error occurred'
    responseTime.value = Date.now() - startTime
  } finally {
    loading.value = false
  }
}

const testNetworkApi = async () => {
  if (!currentUser.value || !networkId.value.trim()) return

  loading.value = true
  error.value = ''
  apiResponse.value = null

  const startTime = Date.now()

  try {
    const url = new URL('/api/v1/talent/network', window.location.origin)
    url.searchParams.append('network_id', networkId.value.trim())

    console.log('Making Network API request:', {
      url: url.toString(),
      headers: {
        'Authorization': `Bearer ${currentUser.value.uid}`
      }
    })

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${currentUser.value.uid}`,
        'Content-Type': 'application/json',
      },
    })

    responseTime.value = Date.now() - startTime
    lastRequestStatus.value = `${response.status} ${response.statusText}`

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const data = await response.json()
    apiResponse.value = data

    console.log('Network API Response:', data)

  } catch (err: any) {
    console.error('Network API Error:', err)
    error.value = err.message || 'Unknown error occurred'
    responseTime.value = Date.now() - startTime
  } finally {
    loading.value = false
  }
}

const testEmailApi = async () => {
  if (!currentUser.value || !profileId.value.trim()) return

  loading.value = true
  error.value = ''
  apiResponse.value = null
  const startTime = Date.now()

  try {
    const url = new URL('/api/v1/talent/email', window.location.origin)
    url.searchParams.append('profile_id', profileId.value.trim())

    console.log('Making Email API request:', {
      url: url.toString(),
      headers: {
        'Authorization': `Bearer ${currentUser.value.uid}`
      }
    })

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${currentUser.value.uid}`,
        'Content-Type': 'application/json',
      },
    })

    responseTime.value = Date.now() - startTime
    lastRequestStatus.value = `${response.status} ${response.statusText}`

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const data = await response.json()
    apiResponse.value = data

    console.log('Email API Response:', data)

  } catch (err: any) {
    console.error('Email API Error:', err)
    error.value = err.message || 'Unknown error occurred'
    responseTime.value = Date.now() - startTime
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Custom scrollbar for response area */
pre::-webkit-scrollbar {
  width: 8px;
}

pre::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

pre::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

pre::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark pre::-webkit-scrollbar-track {
  background: #374151;
}

.dark pre::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark pre::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
